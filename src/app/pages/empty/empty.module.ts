import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { EmptyRoutingModule } from './empty-routing.module';
import { EmptyComponent } from './empty.component';
import { BaThemeSpinner } from 'src/app/common/services/baThemeSpinner/baThemeSpinner.service';


@NgModule({
  declarations: [EmptyComponent],
  imports: [
    CommonModule,
    EmptyRoutingModule
  ]
})
export class EmptyModule {
  constructor(private readonly _spinner: BaThemeSpinner) {
      this._spinner.hide();
  }
}
